/**
 * Global respondent details options for survey components
 * Used in both create survey and edit survey components
 */

/**
 * Interface for option items used in select, radio, and checkbox inputs
 */
export interface SelectOption {
  value: string;
  label: string;
}

/**
 * Enhanced interface for respondent detail options with flexible configuration
 */
export interface RespondentDetailOption {
  value: string; // internal name (e.g., "email")
  label: string; // label shown to user
  inputType: string; // e.g., "text", "select", "email", "radio", "checkbox", "number"
  required?: boolean; // whether the field is required
  placeholder?: string; // optional placeholder text
  options?: SelectOption[]; // only for select/radio/checkbox fields
  defaultValue?: any; // prefilled value if any
}

/**
 * List of accepted input types for respondent details
 */
export const AcceptedInputTypes = [
  { value: 'text', label: 'Text' },
  { value: 'email', label: 'Email' },
  { value: 'select', label: 'Dropdown' },
  { value: 'radio', label: 'Single Choice' },
  { value: 'checkbox', label: 'Multi Choice' },
  { value: 'number', label: 'Number' },
];

/**
 * Legacy respondent details options - kept for backward compatibility
 */
export const RespondentDetailsOptions: RespondentDetailOption[] = [
  { value: '-', label: 'Choose Respondent Details', inputType: 'text' },
  {
    value: 'fullName',
    label: 'Full Name',
    inputType: 'text',
    placeholder: 'Enter your full name',
    required: true,
  },
  {
    value: 'email',
    label: 'Email',
    inputType: 'email',
    placeholder: 'Enter your email address',
    required: true,
  },
  {
    value: 'age',
    label: 'Age',
    inputType: 'select',
    placeholder: 'Select your age range',
    required: true,
    options: [
      { value: 'under_18', label: 'Under 18' },
      { value: '18_24', label: '18-24' },
      { value: '25_34', label: '25-34' },
      { value: '35_44', label: '35-44' },
      { value: '45_54', label: '45-54' },
      { value: '55_64', label: '55-64' },
      { value: '65_plus', label: '65+' },
    ],
  },
  {
    value: 'gender',
    label: 'Gender',
    inputType: 'select',
    placeholder: 'Select your gender',
    required: true,
    options: [
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' },
      { value: 'non_binary', label: 'Non-binary' },
      { value: 'prefer_not_to_say', label: 'Prefer not to say' },
    ],
  },
  {
    value: 'jobTitle',
    label: 'Job Title / Role',
    inputType: 'text',
    placeholder: 'Enter your job title',
    required: true,
  },
  {
    value: 'seniority',
    label: 'Seniority Level',
    inputType: 'select',
    placeholder: 'Select your seniority level',
    required: true,
    options: [
      { value: 'entry', label: 'Entry Level' },
      { value: 'mid', label: 'Mid Level' },
      { value: 'senior', label: 'Senior Level' },
      { value: 'manager', label: 'Manager' },
      { value: 'director', label: 'Director' },
      { value: 'executive', label: 'Executive' },
    ],
  },
  {
    value: 'department',
    label: 'Department',
    inputType: 'select',
    placeholder: 'Select your department',
    required: true,
    options: [
      { value: 'engineering', label: 'Engineering' },
      { value: 'product', label: 'Product' },
      { value: 'design', label: 'Design' },
      { value: 'marketing', label: 'Marketing' },
      { value: 'sales', label: 'Sales' },
      { value: 'customer_support', label: 'Customer Support' },
      { value: 'hr', label: 'Human Resources' },
      { value: 'finance', label: 'Finance' },
      { value: 'operations', label: 'Operations' },
      { value: 'other', label: 'Other' },
    ],
  },
  {
    value: 'employmentType',
    label: 'Employment Type',
    inputType: 'select',
    placeholder: 'Select your employment type',
    required: true,
    options: [
      { value: 'full_time', label: 'Full-time' },
      { value: 'part_time', label: 'Part-time' },
      { value: 'contract', label: 'Contract' },
      { value: 'freelance', label: 'Freelance' },
      { value: 'intern', label: 'Intern' },
    ],
  },
  {
    value: 'organisationName',
    label: 'Organisation Name',
    inputType: 'text',
    placeholder: 'Enter your organization name',
    required: true,
  },
  {
    value: 'organisationSize',
    label: 'Organisation Size',
    inputType: 'select',
    placeholder: 'Select your organization size',
    required: true,
    options: [
      { value: '1_10', label: '1-10 employees' },
      { value: '11_50', label: '11-50 employees' },
      { value: '51_200', label: '51-200 employees' },
      { value: '201_500', label: '201-500 employees' },
      { value: '501_1000', label: '501-1000 employees' },
      { value: '1001_plus', label: '1001+ employees' },
    ],
  },
  {
    value: 'industry',
    label: 'Industry',
    inputType: 'select',
    placeholder: 'Select your industry',
    required: true,
    options: [
      { value: 'technologySoftware', label: 'Technology & Software' },
      { value: 'consumerGoodsRetail', label: 'Consumer Goods & Retail' },
      { value: 'healthcareLifeSciences', label: 'Healthcare & Life Sciences' },
      { value: 'educationTraining', label: 'Education & Training' },
      { value: 'bankingFinance', label: 'Banking & Finance' },
      { value: 'businessProfessionalServices', label: 'Business & Professional Services' },
      { value: 'mediaEntertainment', label: 'Media & Entertainment' },
      { value: 'realEstateConstruction', label: 'Real Estate & Construction' },
      { value: 'manufacturingIndustrial', label: 'Manufacturing & Industrial' },
      { value: 'energyEnvironment', label: 'Energy & Environment' },
      { value: 'agricultureNaturalResources', label: 'Agriculture & Natural Resources' },
      { value: 'transportationLogistics', label: 'Transportation & Logistics' },
      { value: 'governmentPublicSector', label: 'Government & Public Sector' },
      { value: 'nonprofitSocialImpact', label: 'Nonprofit & Social Impact' },
      { value: 'hospitalityTravelLeisure', label: 'Hospitality, Travel & Leisure' },
      { value: 'telecommunications', label: 'Telecommunications' },
      { value: 'artsCultureHumanities', label: 'Arts, Culture & Humanities' },
      { value: 'personalServices', label: 'Personal Services' },
      { value: 'foodBeverage', label: 'Food & Beverage' },
      { value: 'automotive', label: 'Automotive' },
      { value: 'aerospace', label: 'Aerospace & Defense' },
      { value: 'insurance', label: 'Insurance' },
      { value: 'legalServices', label: 'Legal Services' },
      { value: 'consulting', label: 'Consulting' },
      { value: 'accounting', label: 'Accounting & Auditing' },
      { value: 'advertising', label: 'Advertising & Marketing' },
      { value: 'architecture', label: 'Architecture & Design' },
      { value: 'engineering', label: 'Engineering' },
      { value: 'research', label: 'Research & Development' },
      { value: 'pharmaceuticals', label: 'Pharmaceuticals' },
      { value: 'biotechnology', label: 'Biotechnology' },
      { value: 'veterinary', label: 'Veterinary Services' },
      { value: 'fitness', label: 'Fitness & Wellness' },
      { value: 'beauty', label: 'Beauty & Cosmetics' },
      { value: 'fashion', label: 'Fashion & Apparel' },
      { value: 'jewelry', label: 'Jewelry & Luxury Goods' },
      { value: 'sports', label: 'Sports & Recreation' },
      { value: 'gaming', label: 'Gaming & Esports' },
      { value: 'publishing', label: 'Publishing & Print Media' },
      { value: 'photography', label: 'Photography & Visual Arts' },
      { value: 'music', label: 'Music & Audio' },
      { value: 'film', label: 'Film & Video Production' },
      { value: 'events', label: 'Events & Conference Management' },
      { value: 'security', label: 'Security Services' },
      { value: 'cleaning', label: 'Cleaning & Maintenance' },
      { value: 'repair', label: 'Repair & Technical Services' },
      { value: 'childcare', label: 'Childcare & Early Education' },
      { value: 'eldercare', label: 'Elder Care & Senior Services' },
      { value: 'socialWork', label: 'Social Work & Community Services' },
      { value: 'religious', label: 'Religious & Spiritual Organizations' },
      { value: 'military', label: 'Military & Armed Forces' },
      { value: 'emergency', label: 'Emergency & Public Safety' },
      { value: 'waste', label: 'Waste Management & Recycling' },
      { value: 'utilities', label: 'Utilities & Infrastructure' },
      { value: 'mining', label: 'Mining & Extraction' },
      { value: 'forestry', label: 'Forestry & Logging' },
      { value: 'fishing', label: 'Fishing & Aquaculture' },
      { value: 'textiles', label: 'Textiles & Fabrics' },
      { value: 'chemicals', label: 'Chemicals & Materials' },
      { value: 'plastics', label: 'Plastics & Polymers' },
      { value: 'metals', label: 'Metals & Metallurgy' },
      { value: 'electronics', label: 'Electronics & Hardware' },
      { value: 'appliances', label: 'Home Appliances & Furniture' },
      { value: 'packaging', label: 'Packaging & Containers' },
      { value: 'printing', label: 'Printing & Graphics' },
      { value: 'maritime', label: 'Maritime & Shipping' },
      { value: 'aviation', label: 'Aviation & Airlines' },
      { value: 'railway', label: 'Railway & Public Transit' },
      { value: 'trucking', label: 'Trucking & Freight' },
      { value: 'warehousing', label: 'Warehousing & Storage' },
      { value: 'ecommerce', label: 'E-commerce & Online Retail' },
      { value: 'wholesale', label: 'Wholesale & Distribution' },
      { value: 'import', label: 'Import & Export' },
      { value: 'franchising', label: 'Franchising' },
      { value: 'startups', label: 'Startups & Entrepreneurship' },
      { value: 'investment', label: 'Investment & Venture Capital' },
      { value: 'cryptocurrency', label: 'Cryptocurrency & Blockchain' },
      { value: 'student', label: 'Student' },
      { value: 'unemployed', label: 'Unemployed / Job Seeking' },
      { value: 'retired', label: 'Retired' },
      { value: 'homemaker', label: 'Homemaker / Stay-at-home Parent' },
      { value: 'freelancer', label: 'Freelancer / Independent Contractor' },
      { value: 'volunteer', label: 'Volunteer Work' },
      { value: 'disabled', label: 'Unable to Work / Disabled' },
      { value: 'other', label: 'Other Industry' },
      { value: 'preferNotToSay', label: 'Prefer Not to Say' },
    ],
  },
  { value: 'custom', label: '+ Create Custom Detail', inputType: 'text' },
];
